{"mail": {"title": "Mail Templates", "single": "Mail Template", "list": "Mail Template List", "add": "Add Template", "edit": "Edit Template", "delete": "Delete Template", "preview": "Preview", "sendTest": "Send Test Mail", "noTemplates": "No mail templates found", "createTemplate": "Create New Mail Template", "editTemplate": "Edit Mail Template", "deleteTemplate": "Delete Mail Template", "previewTemplate": "Template Preview", "testMail": "Test Mail", "fields": {"shortCode": "Short Code", "name": "Template Name", "description": "Description", "subject": "Mail Subject", "content": "Mail Content", "category": "Category", "variables": "Variables", "defaultFromEmail": "Default From Email", "defaultFromName": "Default From Name", "isActive": "Active", "isSystem": "System Template", "sortOrder": "Sort Order", "createdAt": "Created At", "updatedAt": "Updated At", "recipientEmail": "Recipient Email"}, "categories": {"shipping": "Shipping", "order": "Order", "user": "User", "system": "System", "notification": "Notification"}, "status": {"active": "Active", "inactive": "Inactive", "system": "System", "custom": "Custom"}, "actions": {"create": "Create", "update": "Update", "delete": "Delete", "preview": "Preview", "sendTest": "Send Test", "toggleActive": "Toggle Active", "insertVariable": "Insert Variable", "selectTemplate": "Select Template"}, "messages": {"createSuccess": "Mail template created successfully", "updateSuccess": "Mail template updated successfully", "deleteSuccess": "Mail template deleted successfully", "deleteConfirm": "Are you sure you want to delete this mail template?", "systemTemplateDeleteError": "System templates cannot be deleted", "testMailSent": "Test mail sent successfully", "testMailError": "Error sending test mail", "previewError": "Error generating preview", "shortCodeExists": "This short code is already in use", "invalidVariables": "Invalid variables detected", "contentRequired": "Mail content is required", "subjectRequired": "Mail subject is required", "nameRequired": "Template name is required", "shortCodeRequired": "Short code is required", "createError": "Error creating mail template", "updateError": "Error updating mail template", "deleteError": "Error deleting mail template"}, "placeholders": {"shortCode": "example-template-code", "name": "Enter template name", "description": "Enter template description", "subject": "Enter mail subject (you can use {{variable}})", "content": "Write your mail content...", "defaultFromEmail": "<EMAIL>", "defaultFromName": "B2B Platform", "recipientEmail": "<EMAIL>", "searchTemplates": "Search templates..."}, "variables": {"title": "Available Variables", "description": "You can use the following variables in your mail content", "format": "Variables are used in {{variableName}} format", "shipping": {"customerName": "Customer Name", "orderNumber": "Order Number", "carrierName": "Carrier Name", "trackingNumber": "Tracking Number", "trackingUrl": "Tracking URL", "deliveryAddress": "Delivery Address", "estimatedDelivery": "Estimated Delivery", "cargoKey": "Cargo Key", "invoiceKey": "Invoice Key", "customerPhone": "Customer Phone", "orderAmount": "Order Amount", "createdAt": "Created At"}, "order": {"customerName": "Customer Name", "orderNumber": "Order Number", "orderDate": "Order Date", "orderAmount": "Order Amount", "orderStatus": "Order Status", "paymentMethod": "Payment Method", "deliveryAddress": "Delivery Address", "orderItems": "Order Items", "customerEmail": "Customer <PERSON><PERSON>", "customerPhone": "Customer Phone"}, "user": {"customerName": "Customer Name", "customerEmail": "Customer <PERSON><PERSON>", "activationLink": "Activation Link", "resetPasswordLink": "Reset Password Link", "loginUrl": "Login URL", "companyName": "Company Name", "welcomeMessage": "Welcome Message"}, "system": {"systemName": "System Name", "maintenanceDate": "Maintenance Date", "maintenanceTime": "Maintenance Time", "estimatedDuration": "Estimated Duration", "contactEmail": "Contact Email", "supportPhone": "Support Phone"}, "notification": {"notificationTitle": "Notification Title", "notificationMessage": "Notification Message", "actionUrl": "Action URL", "actionText": "Action Text", "customerName": "Customer Name", "date": "Date", "time": "Time"}}, "predefined": {"title": "Predefined Templates", "description": "Pre-defined mail templates by the system", "templates": {"customer-shipment-notification": "Customer Shipment Notification", "admin-shipment-notification": "Admin Shipment Notification", "order-confirmation": "Order Confirmation", "order-cancellation": "Order Cancellation", "payment-confirmation": "Payment Confirmation", "welcome-email": "Welcome Email", "password-reset": "Password Reset", "account-activation": "Account Activation", "stock-alert": "<PERSON>", "maintenance-notification": "Maintenance Notification", "basket-product-natification": "Baskette Kalan Ürün <PERSON>ırlatma"}}, "tabs": {"general": "General", "content": "Content", "variables": "Variables", "preview": "Preview", "settings": "Settings"}, "help": {"shortCode": "Enter a unique short code for the template. This code is used by the system to identify the template.", "variables": "You can use variables in {{variableName}} format in mail content. Variables are replaced with actual values when sending mail.", "category": "Specifies which category the template belongs to. This is used to organize templates.", "systemTemplate": "System templates are pre-defined templates and cannot be deleted.", "preview": "Use the preview feature to test how your template will look."}}}