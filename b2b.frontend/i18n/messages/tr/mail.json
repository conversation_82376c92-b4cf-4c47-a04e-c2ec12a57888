{"mail": {"title": "Mail Şablonları", "single": "Mail Şablonu", "list": "Mail Şablonu Listesi", "add": "<PERSON><PERSON><PERSON>", "edit": "Şablon <PERSON>", "delete": "Şablon Sil", "preview": "<PERSON><PERSON><PERSON><PERSON>", "sendTest": "Test <PERSON><PERSON>", "noTemplates": "Mail şablonu bulunamadı", "createTemplate": "Yeni Mail Şablonu Oluştur", "editTemplate": "Mail Şablonunu <PERSON>le", "deleteTemplate": "Mail Şablonunu Sil", "previewTemplate": "Ş<PERSON><PERSON>", "testMail": "Test Maili", "fields": {"shortCode": "<PERSON><PERSON><PERSON>", "name": "Şablon Adı", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "subject": "Mail Konusu", "content": "Mail İçeriği", "category": "<PERSON><PERSON><PERSON>", "variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultFromEmail": "Varsayılan Gönderici E-posta", "defaultFromName": "Varsayılan Gönderici Adı", "isActive": "Aktif", "isSystem": "Sistem Şablonu", "sortOrder": "Sıralama", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recipientEmail": "Alıcı E-posta"}, "categories": {"shipping": "<PERSON><PERSON>", "order": "Sipariş", "user": "Kullanıcı", "system": "Sistem", "notification": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "system": "Sistem", "custom": "<PERSON><PERSON>"}, "actions": {"create": "Oluştur", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "preview": "<PERSON><PERSON><PERSON>", "sendTest": "<PERSON> Gönder", "toggleActive": "Aktif/Pasif", "insertVariable": "Değişken <PERSON>", "selectTemplate": "Şablon Seç"}, "messages": {"createSuccess": "Mail şablonu başarıyla oluşturuldu", "updateSuccess": "Mail şablonu başarıyla güncellendi", "deleteSuccess": "Mail şablonu başarı<PERSON> silindi", "deleteConfirm": "Bu mail şablonunu silmek istediğinizden emin misiniz?", "systemTemplateDeleteError": "Sistem şablonları silinemez", "testMailSent": "Test maili başarıyla gö<PERSON>ildi", "testMailError": "Test maili gönderilirken hata o<PERSON>", "previewError": "<PERSON><PERSON>zleme oluşturulurken hata oluştu", "shortCodeExists": "Bu kısa kod zaten kullanılıyor", "invalidVariables": "Geçersiz değişkenler tespit edildi", "contentRequired": "Mail içeriği zorunludur", "subjectRequired": "Mail konusu zorunludur", "nameRequired": "Şablon adı zorunludur", "shortCodeRequired": "Kısa kod zorunludur", "createError": "Mail şablonu oluşturulurken hata oluştu", "updateError": "Mail şablonu güncellenirken hata oluştu", "deleteError": "Mail şablonu silinirken hata oluştu"}, "placeholders": {"shortCode": "ornek-sablon-kodu", "name": "Şablon adını girin", "description": "Şablon açıklamasını girin", "subject": "Mail konusunu girin ({{de<PERSON><PERSON>ş<PERSON>}} kullanabilirsiniz)", "content": "Mail içeriğinizi yazın...", "defaultFromEmail": "<EMAIL>", "defaultFromName": "B2B Platform", "recipientEmail": "<EMAIL>", "searchTemplates": "Şablonlarda ara..."}, "variables": {"title": "Kullanılabil<PERSON>", "description": "Aşağıdaki değişkenleri mail içeriğinde kullanabilirsiniz", "format": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{değişkenAdı}} <PERSON><PERSON><PERSON> kullanılır", "shipping": {"customerName": "Müşteri Adı", "orderNumber": "Sipariş Numarası", "carrierName": "Kargo Firması", "trackingNumber": "Takip <PERSON>", "trackingUrl": "Takip URL'si", "deliveryAddress": "Teslimat Adresi", "estimatedDelivery": "<PERSON><PERSON><PERSON>", "cargoKey": "<PERSON>rgo <PERSON>", "invoiceKey": "<PERSON><PERSON>", "customerPhone": "Müşteri Telefonu", "orderAmount": "Sipariş Tutarı", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "order": {"customerName": "Müşteri Adı", "orderNumber": "Sipariş Numarası", "orderDate": "Sipariş Tarihi", "orderAmount": "Sipariş Tutarı", "orderStatus": "Sipariş Durumu", "paymentMethod": "<PERSON><PERSON><PERSON>", "deliveryAddress": "Teslimat Adresi", "orderItems": "Sipariş Kalemleri", "customerEmail": "Müşteri E-posta", "customerPhone": "Müşteri Telefonu"}, "user": {"customerName": "Müşteri Adı", "customerEmail": "Müşteri E-posta", "activationLink": "Aktivasyon Linki", "resetPasswordLink": "Şifre Sıfırlama Linki", "loginUrl": "G<PERSON>ş URL'si", "companyName": "Şirket Adı", "welcomeMessage": "Hoş Geldin <PERSON>"}, "system": {"systemName": "Sistem Adı", "maintenanceDate": "Bakım <PERSON>", "maintenanceTime": "Bakım <PERSON>", "estimatedDuration": "<PERSON><PERSON><PERSON>", "contactEmail": "İletişim E-posta", "supportPhone": "Destek Telefonu"}, "notification": {"notificationTitle": "<PERSON><PERSON><PERSON><PERSON>", "notificationMessage": "<PERSON><PERSON><PERSON><PERSON>", "actionUrl": "Aksiyon URL'si", "actionText": "<PERSON><PERSON><PERSON><PERSON>", "customerName": "Müşteri Adı", "date": "<PERSON><PERSON><PERSON>", "time": "Saat"}}, "predefined": {"title": "Önceden Tanımlı Şablonlar", "description": "Sistem tarafından önceden tanımlanmış mail şablonları", "templates": {"customer-shipment-notification": "Müşteri Kargo Bildirimi", "admin-shipment-notification": "<PERSON><PERSON>", "order-confirmation": "Sipariş Onayı", "order-cancellation": "Sipariş İptal Bildirimi", "payment-confirmation": "Ödeme <PERSON>", "welcome-email": "<PERSON><PERSON>", "password-reset": "Şifre <PERSON>ırl<PERSON>", "account-activation": "Hesap <PERSON>", "stock-alert": "Stok Uyarısı", "maintenance-notification": "Sistem Bakım Bildirimi", "basket-product-natification": "Baskette Kalan Ürün <PERSON>ırlatma"}}, "tabs": {"general": "<PERSON><PERSON>", "content": "İçerik", "variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>"}, "help": {"shortCode": "Şablon için ben<PERSON>iz bir kısa kod girin. Bu kod sistem tarafından şablonu tanımlamak için kullanılır.", "variables": "Mail içeriğinde {{değişkenAdı}} formatında değişkenler kullanabilirsiniz. Değişkenler mail gönderilirken gerçek değerlerle değiştirilir.", "category": "Şablonun hangi kategoriye ait olduğunu belirtir. Bu, şablonları organize etmek için kullanılır.", "systemTemplate": "Sistem şablonları önceden tanımlanmış şablonlardır ve silinemez.", "preview": "Önizleme özelliği ile şablonunuzun nasıl görüneceğini test edebilirsiniz."}}}