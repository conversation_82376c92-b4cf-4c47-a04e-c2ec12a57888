// Mail Template Types
export interface MailTemplateDto {
  id: string;
  shortCode: string;
  name: string;
  description?: string;
  subject: string;
  content: string;
  variables?: string[];
  defaultFromEmail?: string;
  defaultFromName?: string;
  category?: string;
  isActive: boolean;
  isSystem: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateMailTemplateDto {
  shortCode: string;
  name: string;
  description?: string;
  subject: string;
  content: string;
  variables?: string[];
  defaultFromEmail?: string;
  defaultFromName?: string;
  category?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateMailTemplateDto {
  name?: string;
  description?: string;
  subject?: string;
  content?: string;
  variables?: string[];
  defaultFromEmail?: string;
  defaultFromName?: string;
  category?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface MailTemplatePreviewRequest {
  variables: Record<string, string>;
}

export interface MailTemplatePreviewResponse {
  subject: string;
  content: string;
}

export interface MailTemplateVariablesResponse {
  variables: string[];
}

// Mail Template Categories
export const MAIL_TEMPLATE_CATEGORIES = {
  SHIPPING: 'shipping',
  ORDER: 'order',
  USER: 'user',
  SYSTEM: 'system',
  NOTIFICATION: 'notification'
} as const;

export type MailTemplateCategory = typeof MAIL_TEMPLATE_CATEGORIES[keyof typeof MAIL_TEMPLATE_CATEGORIES];

// Predefined Mail Template Variables for different categories
export const MAIL_TEMPLATE_VARIABLES = {
  [MAIL_TEMPLATE_CATEGORIES.SHIPPING]: [
    'customerName',
    'carrierName',
    'trackingNumber',
    'trackingUrl',
    'deliveryAddress',
    'estimatedDelivery',
    'cargoKey',
    'invoiceKey',
    'customerPhone',
    'createdAt',
    'orderNumber',
    'orderAmount',
    'orderDate',
    'orderItemsTotal',
    'orderItemsDiscountTotal',
    'orderItemsKdvTotal',
    'orderItemsCargo',
    'orderItems',
    'orderRowPrice',
    'orderRowDiscount',
    'orderRowTax',
    'orderRowVariant',
    'orderRowAmount',
    'orderRowImage',
    'sendLinkAddress'
  ],
  [MAIL_TEMPLATE_CATEGORIES.ORDER]: [
    'customerName',
    'customerEmail',
    'customerPhone',
    'orderNumber',
    'orderDate',
    'orderAmount',
    'orderStatus',
    'paymentMethod',
    'deliveryAddress',
    'deliveryCity',
    'deliveryCounty',
    'deliveryCountry',
    'orderRowName',
    'orderRowPrice',
    'orderRowDiscount',
    'orderRowTax',
    'orderRowVariant',
    'orderRowAmount',
    'orderRowImage',
    'orderItemsTotal',
    'orderItemsDiscountTotal',
    'orderItemsKdvTotal',
    'orderItemsCargo',
    'shippingAddress',
    'shippingCity',
    'shippingCounty',
    'shippingCountry',
    'billingAddress',
    'billingCity',
    'billingCounty',
    'billingCountry',
    'sendLinkAddress',
    'couponCode'
  ],
  [MAIL_TEMPLATE_CATEGORIES.USER]: [
    'customerName',
    'customerEmail',
    'activationLink',
    'resetPasswordLink',
    'loginUrl',
    'companyName',
    'welcomeMessage',
    'sendLinkAddress',
    'couponCode'
  ],
  [MAIL_TEMPLATE_CATEGORIES.SYSTEM]: [
    'systemName',
    'maintenanceDate',
    'maintenanceTime',
    'estimatedDuration',
    'contactEmail',
    'supportPhone'
  ],
  [MAIL_TEMPLATE_CATEGORIES.NOTIFICATION]: [
    'notificationTitle',
    'notificationMessage',
    'actionUrl',
    'actionText',
    'customerName',
    'date',
    'time',
    'ProductImageUrl',
    'ProductName',
    'ProductPrice',
    'ActionLink'
  ]
} as const;

// Hard-coded Mail Template Definitions
export const PREDEFINED_MAIL_TEMPLATES = {
  'customer-shipment-notification': {
    name: 'Müşteri Kargo Bildirimi',
    category: MAIL_TEMPLATE_CATEGORIES.SHIPPING,
    description: 'Müşteriye kargo gönderildiğinde gönderilen bildirim maili',
    defaultSubject: 'Siparişiniz Kargoya Verildi - {{orderNumber}}',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.SHIPPING]
  },
  'admin-shipment-notification': {
    name: 'Admin Kargo Bildirimi',
    category: MAIL_TEMPLATE_CATEGORIES.SHIPPING,
    description: 'Yeni kargo oluşturulduğunda admin\'e gönderilen bildirim',
    defaultSubject: 'Yeni Kargo Oluşturuldu - {{orderNumber}}',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.SHIPPING]
  },
  'order-confirmation': {
    name: 'Sipariş Onayı',
    category: MAIL_TEMPLATE_CATEGORIES.ORDER,
    description: 'Sipariş onaylandığında müşteriye gönderilen mail',
    defaultSubject: 'Siparişiniz Onaylandı - {{orderNumber}}',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.ORDER]
  },
  'order-cancellation': {
    name: 'Sipariş İptal Bildirimi',
    category: MAIL_TEMPLATE_CATEGORIES.ORDER,
    description: 'Sipariş iptal edildiğinde müşteriye gönderilen bildirim',
    defaultSubject: 'Siparişiniz İptal Edildi - {{orderNumber}}',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.ORDER]
  },
  'payment-confirmation': {
    name: 'Ödeme Onayı',
    category: MAIL_TEMPLATE_CATEGORIES.ORDER,
    description: 'Ödeme onaylandığında müşteriye gönderilen bildirim',
    defaultSubject: 'Ödemeniz Alındı - {{orderNumber}}',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.ORDER]
  },
  'welcome-email': {
    name: 'Hoş Geldin Maili',
    category: MAIL_TEMPLATE_CATEGORIES.USER,
    description: 'Yeni kullanıcı kaydında gönderilen hoş geldin maili',
    defaultSubject: 'Hoş Geldiniz {{customerName}}!',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.USER]
  },
  'password-reset': {
    name: 'Şifre Sıfırlama',
    category: MAIL_TEMPLATE_CATEGORIES.USER,
    description: 'Şifre sıfırlama isteğinde gönderilen mail',
    defaultSubject: 'Şifre Sıfırlama Talebi',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.USER]
  },
  'account-activation': {
    name: 'Hesap Aktivasyonu',
    category: MAIL_TEMPLATE_CATEGORIES.USER,
    description: 'Hesap aktivasyonu için gönderilen mail',
    defaultSubject: 'Hesabınızı Aktifleştirin',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.USER]
  },
  'stock-alert': {
    name: 'Stok Uyarısı',
    category: MAIL_TEMPLATE_CATEGORIES.SYSTEM,
    description: 'Stok azaldığında gönderilen uyarı maili',
    defaultSubject: 'Stok Uyarısı - Kritik Seviye',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.SYSTEM]
  },
  'maintenance-notification': {
    name: 'Sistem Bakım Bildirimi',
    category: MAIL_TEMPLATE_CATEGORIES.SYSTEM,
    description: 'Sistem bakımı öncesi gönderilen bildirim',
    defaultSubject: 'Sistem Bakım Bildirimi - {{maintenanceDate}}',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.SYSTEM]
  },
  'basket-product-notification': {
    name: 'Baskette Kalan Ürün Bildirimi',
    category: MAIL_TEMPLATE_CATEGORIES.USER,
    description: 'Baskette Kalan Ürün Bildirimi',
    defaultSubject: 'Sepetinizde ürün bıraktınız!!!',
    variables: MAIL_TEMPLATE_VARIABLES[MAIL_TEMPLATE_CATEGORIES.USER]
  }
} as const;

export type PredefinedMailTemplateKey = keyof typeof PREDEFINED_MAIL_TEMPLATES;
