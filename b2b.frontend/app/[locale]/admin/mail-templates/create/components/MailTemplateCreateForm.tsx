'use client';

import React, { useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import TiptapEditor from '@/components/ui/tiptap-editor';
import CkeditorEditor from '@/components/ui/ckeditor5';
import { toast } from 'sonner';
import { useCreateMailTemplate, usePreviewMailTemplate } from '@/lib/api/hooks/useMailTemplates';
import {
  MAIL_TEMPLATE_CATEGORIES,
  MAIL_TEMPLATE_VARIABLES,
  PREDEFINED_MAIL_TEMPLATES,
  type CreateMailTemplateDto,
  type MailTemplateCategory
} from '@/types/mail-template';
import { Eye, Save, Wand2 } from 'lucide-react';

// Form validation schema
const createMailTemplateSchema = z.object({
  shortCode: z.string().min(1, 'Short code is required').max(100),
  name: z.string().min(1, 'Template name is required').max(200),
  description: z.string().max(500).optional(),
  subject: z.string().min(1, 'Mail subject is required').max(500),
  content: z.string().min(1, 'Mail content is required'),
  category: z.string().optional(),
  defaultFromEmail: z.string().email().optional().or(z.literal('')),
  defaultFromName: z.string().max(200).optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().default(0),
});

type CreateMailTemplateForm = z.infer<typeof createMailTemplateSchema>;

export default function MailTemplateCreateForm() {
  const t = useTranslations('mail');
  const tCommon = useTranslations('common');
  const router = useRouter();

  const [selectedVariables, setSelectedVariables] = useState<string[]>([]);
  const [previewData, setPreviewData] = useState<{ subject: string; content: string } | null>(null);
  const [previewVariables, setPreviewVariables] = useState<Record<string, string>>({});

  const createMutation = useCreateMailTemplate();
  const previewMutation = usePreviewMailTemplate();

  const form = useForm<CreateMailTemplateForm>({
    resolver: zodResolver(createMailTemplateSchema),
    defaultValues: {
      shortCode: '',
      name: '',
      description: '',
      subject: '',
      content: '',
      category: '',
      defaultFromEmail: '',
      defaultFromName: 'B2B Platform',
      isActive: true,
      sortOrder: 0,
    },
  });

  const watchedCategory = form.watch('category') as MailTemplateCategory;
  const watchedContent = form.watch('content');
  const watchedSubject = form.watch('subject');

  // Get available variables based on selected category
  const availableVariables = watchedCategory
    ? MAIL_TEMPLATE_VARIABLES[watchedCategory] || []
    : [];

  // Load predefined template
  const loadPredefinedTemplate = (templateKey: string) => {
    const template = PREDEFINED_MAIL_TEMPLATES[templateKey as keyof typeof PREDEFINED_MAIL_TEMPLATES];
    if (template) {
      form.setValue('shortCode', templateKey);
      form.setValue('name', template.name);
      form.setValue('description', template.description);
      form.setValue('subject', template.defaultSubject);
      form.setValue('category', template.category);
      setSelectedVariables([...template.variables]);
    }
  };

  // Handle form submission
  const onSubmit = async (data: CreateMailTemplateForm) => {
    try {
      const payload: CreateMailTemplateDto = {
        ...data,
        variables: selectedVariables,
      };

      await createMutation.mutateAsync(payload);
      toast.success(t('messages.createSuccess'));
      router.push('/admin/mail-templates');
    } catch (error) {
      toast.error(t('messages.createError'));
    }
  };

  // Handle variable insertion
  const handleVariableInsert = (variable: string) => {
    if (!selectedVariables.includes(variable)) {
      setSelectedVariables([...selectedVariables, variable]);
    }
  };

  // Remove variable
  const removeVariable = (variable: string) => {
    setSelectedVariables(selectedVariables.filter(v => v !== variable));
  };

  // Generate preview
  const generatePreview = async () => {
    if (!watchedContent || !watchedSubject) {
      toast.error(t('messages.contentRequired'));
      return;
    }

    // Create sample variables for preview
    const sampleVariables: Record<string, string> = {};
    selectedVariables.forEach(variable => {
      sampleVariables[variable] = previewVariables[variable] || `[${variable}]`;
    });

    try {
      // For preview, we'll simulate the rendering locally since we don't have a shortCode yet
      let previewSubject = watchedSubject;
      let previewContent = watchedContent;

      Object.entries(sampleVariables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        previewSubject = previewSubject.replace(regex, value);
        previewContent = previewContent.replace(regex, value);
      });

      setPreviewData({
        subject: previewSubject,
        content: previewContent
      });
    } catch (error) {
      toast.error(t('messages.previewError'));
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs defaultValue="general" className="space-y-6">
          <TabsList>
            <TabsTrigger value="general">{t('tabs.general')}</TabsTrigger>
            <TabsTrigger value="content">{t('tabs.content')}</TabsTrigger>
            <TabsTrigger value="variables">{t('tabs.variables')}</TabsTrigger>
            <TabsTrigger value="preview">{t('tabs.preview')}</TabsTrigger>
          </TabsList>

          {/* General Tab */}
          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('tabs.general')}</CardTitle>
                <CardDescription>
                  {t('help.shortCode')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Predefined Templates */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t('predefined.title')}</label>
                  <Select onValueChange={loadPredefinedTemplate}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('actions.selectTemplate')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(PREDEFINED_MAIL_TEMPLATES).map(([key, template]) => (
                        <SelectItem key={key} value={key}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="defaultFromEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fields.defaultFromEmail')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('placeholders.defaultFromEmail')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="defaultFromName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fields.defaultFromName')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('placeholders.defaultFromName')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="shortCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fields.shortCode')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('placeholders.shortCode')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fields.name')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('placeholders.name')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('fields.description')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('placeholders.description')}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fields.category')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('fields.category')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.values(MAIL_TEMPLATE_CATEGORIES).map((category) => (
                              <SelectItem key={category} value={category}>
                                {t(`categories.${category}`)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>{t('fields.isActive')}</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('tabs.content')}</CardTitle>
                <CardDescription>
                  {t('help.variables')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                             {/* Available Variables */}
                              {availableVariables.length > 0 && (
                                <div className="space-y-2">
                                  <label className="text-sm font-medium">
                                    {t('variables.title')} ({t(`categories.${watchedCategory}`)})
                                  </label>
                                  <div className="flex flex-wrap gap-2">
                                    {availableVariables.map((variable) => (
                                      <Button
                                        key={variable}
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                          navigator.clipboard.writeText(`{{${variable}}}`);
                                          alert("📋 Kopyalandı!");
                                        }}
                                        className="text-xs"
                                      >
                                        {`{{${variable}}}`}
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              )}
                              </CardContent>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('fields.subject')}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('placeholders.subject')}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('fields.content')}</FormLabel>
                      <FormControl>
                        {/* <TiptapEditor
                          content={field.value}
                          onChange={field.onChange}
                          placeholder={t('placeholders.content')}
                          variables={availableVariables}
                          onVariableInsert={handleVariableInsert}
                        /> */}
                        <CkeditorEditor
                          content={field.value}
                          onChange={field.onChange}
                          placeholder={t('placeholders.content')}
                        />

                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Variables Tab */}
          <TabsContent value="variables" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('variables.title')}</CardTitle>
                <CardDescription>
                  {t('variables.description')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Available Variables */}
                {availableVariables.length > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {t('variables.title')} ({t(`categories.${watchedCategory}`)})
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {availableVariables.map((variable) => (
                        <Button
                          key={variable}
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleVariableInsert(variable)}
                          className="text-xs"
                        >
                          {`{{${variable}}}`}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Selected Variables */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Seçili Değişkenler</label>
                  <div className="flex flex-wrap gap-2">
                    {selectedVariables.map((variable) => (
                      <Badge
                        key={variable}
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => removeVariable(variable)}
                      >
                        {`{{${variable}}}`} ×
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('tabs.preview')}</CardTitle>
                <CardDescription>
                  {t('help.preview')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Preview Variables Input */}
                {selectedVariables.length > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Test Değerleri</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedVariables.map((variable) => (
                        <div key={variable} className="space-y-1">
                          <label className="text-xs text-muted-foreground">
                            {`{{${variable}}}`}
                          </label>
                          <Input
                            placeholder={`${variable} değeri`}
                            value={previewVariables[variable] || ''}
                            onChange={(e) =>
                              setPreviewVariables(prev => ({
                                ...prev,
                                [variable]: e.target.value
                              }))
                            }
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Button
                  type="button"
                  onClick={generatePreview}
                  className="w-full"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {t('actions.preview')}
                </Button>

                {/* Preview Result */}
                {previewData && (
                  <div className="space-y-4 border rounded-lg p-4">
                    <div>
                      <label className="text-sm font-medium">Konu:</label>
                      <p className="text-sm bg-muted p-2 rounded mt-1">
                        {previewData.subject}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">İçerik:</label>
                      <div
                        className="prose prose-sm max-w-none bg-muted p-4 rounded mt-1"
                        dangerouslySetInnerHTML={{ __html: previewData.content }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Form Actions */}
        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/mail-templates')}
          >
            {tCommon('actions.cancel')}
          </Button>
          <Button
            type="submit"
            disabled={createMutation.isPending}
          >
            <Save className="h-4 w-4 mr-2" />
            {createMutation.isPending ? 'Kaydediliyor...' : t('actions.create')}
          </Button>
        </div>
      </form>
    </Form>
  );
}
