{"name": "b2b.frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ckeditor/ckeditor5-alignment": "^46.0.3", "@ckeditor/ckeditor5-basic-styles": "^46.0.3", "@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-editor-classic": "^46.0.3", "@ckeditor/ckeditor5-essentials": "^46.0.3", "@ckeditor/ckeditor5-heading": "^46.0.3", "@ckeditor/ckeditor5-link": "^46.0.3", "@ckeditor/ckeditor5-list": "^46.0.3", "@ckeditor/ckeditor5-paragraph": "^46.0.3", "@ckeditor/ckeditor5-react": "^11.0.0", "@ckeditor/ckeditor5-source-editing": "^46.0.3", "@ckeditor/ckeditor5-table": "^46.0.3", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-query": "^5.76.0", "@tiptap/extension-bold": "^2.12.0", "@tiptap/extension-bullet-list": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-document": "^2.10.3", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-italic": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-list-item": "^2.10.3", "@tiptap/extension-ordered-list": "^2.12.0", "@tiptap/extension-paragraph": "^2.10.3", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text": "^2.10.3", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.10.3", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.10.3", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.11.0", "input-otp": "^1.4.2", "lucide-react": "^0.510.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.3", "react-image-crop": "^11.0.10", "react-resizable-panels": "^3.0.1", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}