# B2B Project Environment Configuration Guide

Bu doküman B2B projesindeki tüm ortam değişkenlerinin merkezi yönetimi için hazırlanmıştır.

## 📁 Dosya Yapısı

### Mevcut Ortam Dosyaları
Projede şu anda aşağıdaki ortam dosyaları bulunmaktadır:

```
B2B/
├── .env                                    # Ana ortam dosyası (mevcut)
├── .env.example                           # Örnek ortam dosyası
├── .env.full                             # TÜM ortam değişkenleri (YENİ)
├── B2B.Backend/
│   ├── ApiGateway/
│   │   ├── appsettings.json
│   │   └── appsettings.Development.json
│   ├── MailAPI/
│   │   ├── appsettings.json
│   │   └── appsettings.Development.json
│   ├── MediaAPI/
│   │   ├── appsettings.json
│   │   └── appsettings.Development.json
│   ├── PanelApi/
│   │   ├── appsettings.json
│   │   └── appsettings.Development.json
│   └── WebApi/
│       ├── .env
│       ├── appsettings.json
│       └── appsettings.Development.json
├── b2b.frontend/
│   ├── .env
│   ├── .env.example
│   └── .env.example2
└── themes/vineta/
    ├── .env
    └── .env.local
```

## 🎯 .env.full Dosyası

`.env.full` dosyası tüm projelerdeki ortam değişkenlerini tek bir yerde toplar. Bu dosya:

- ✅ Tüm backend API'lerin appsettings.json değerlerini içerir
- ✅ Tüm frontend .env dosyalarındaki değerleri içerir
- ✅ Docker compose konfigürasyonunu içerir
- ✅ Production ve development ayarlarını içerir
- ✅ Detaylı açıklamalar ve kategoriler içerir

## 🚀 Canlıya Alım Süreci

### 1. Production Ortamı İçin
```bash
# .env.full dosyasını .env olarak kopyala
cp .env.full .env

# Production değerlerini güncelle
nano .env
```

### 2. Güncellenmesi Gereken Değerler
Production ortamında mutlaka güncellenecek değerler:

```bash
# Database
POSTGRES_PASSWORD=güçlü-şifre-buraya
PGADMIN_PASSWORD=güçlü-şifre-buraya

# Domain'ler
NEXT_PUBLIC_API_GATEWAY_URL=https://yourdomain.com
NEXTAUTH_URL=https://panel.yourdomain.com

# SMTP
SmtpSettings__Username=gerçek<EMAIL>
SmtpSettings__Password=gerçek-smtp-şifresi
SmtpSettings__SenderEmail=<EMAIL>

# JWT Secrets (yeni generate edin)
JwtSecret=yeni-güçlü-jwt-secret
NEXTAUTH_SECRET=yeni-güçlü-nextauth-secret

# CORS Hosts
CorsHosts__4=https://yourdomain.com
CorsHosts__5=https://panel.yourdomain.com
CorsHosts__6=https://api.yourdomain.com
```

## 🔧 Development Ortamı

Development için ayrı bir dosya oluşturun:

```bash
# Development için
cp .env.full .env.development

# Development değerlerini güncelle
nano .env.development
```

Development'ta değiştirilecek değerler:
```bash
# Database (localhost)
ConnectionStrings__VeriTabani=Host=localhost;Port=5432;Database=b2b;Username=ukscreative;Password=UksCreative@2025!

# API URLs (localhost)
NEXT_PUBLIC_API_GATEWAY_URL=http://localhost:33800
NEXT_PUBLIC_API_URL=http://localhost:33800/admin-api
NEXTAUTH_URL=http://localhost:3000

# RabbitMQ (localhost)
RabbitMQ__Host=localhost

# Storage (localhost)
StorageSettings__BaseUrl=http://localhost:33802/images
```

## 📋 Ortam Değişkenleri Kategorileri

### 🗄️ Database Configuration
- PostgreSQL bağlantı bilgileri
- PgAdmin konfigürasyonu

### 🔐 Authentication & Security
- JWT secrets
- NextAuth konfigürasyonu
- CORS ayarları

### 🌐 API & Gateway Configuration
- API Gateway URL'leri
- Internal API URL'leri
- CORS hosts

### 📧 Mail Configuration
- SMTP ayarları
- Mail template ayarları

### 📁 File Storage & Media
- Storage strategy
- CDN konfigürasyonu
- Image processing ayarları

### 🚚 Shipping Configuration
- Yurtiçi Kargo ayarları
- Test/Production modları

### 📊 Logging Configuration
- Log seviyeleri
- Framework log ayarları

### 🎨 Theme Configuration
- Vineta theme ayarları
- Lokalizasyon ayarları

## ⚠️ Güvenlik Notları

1. **Şifreleri Değiştirin**: Production'da mutlaka güçlü şifreler kullanın
2. **JWT Secrets**: Yeni JWT secret'ları generate edin
3. **API Keys**: Gerçek API anahtarlarını kullanın
4. **SMTP**: Gerçek mail sunucu bilgilerini kullanın
5. **Domain'ler**: Gerçek domain adreslerinizi kullanın

## 🔄 Güncelleme Süreci

Yeni ortam değişkeni eklendiğinde:

1. İlgili proje dosyasına ekleyin
2. `.env.full` dosyasını güncelleyin
3. Bu README'yi güncelleyin
4. Tüm ortamlarda test edin

## 📞 Destek

Ortam konfigürasyonu ile ilgili sorunlarda:
- Backend API ayarları için appsettings.json dosyalarını kontrol edin
- Frontend ayarları için .env dosyalarını kontrol edin
- Docker ayarları için docker-compose.yml dosyasını kontrol edin
