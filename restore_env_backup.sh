#!/bin/bash

# B2B Project - Environment & Appsettings Files Restore Script
# Bu script backup_env_files/ klasöründeki tüm yedekleri geri yükler

echo "🔄 B2B Project - Environment & Appsettings Files Restore"
echo "=========================================="

# Backup klasörünün varlığını kontrol et
if [ ! -d "backup_env_files" ]; then
    echo "❌ backup_env_files klasörü bulunamadı!"
    echo "Önce yedek alınmış olması gerekiyor."
    exit 1
fi

echo "📁 Yedek dosyalar bulundu:"
find backup_env_files/ -type f | sort

echo ""
read -p "🤔 Bu yedekleri geri yüklemek istediğinizden emin misiniz? (y/N): " confirm

if [[ $confirm != [yY] ]]; then
    echo "❌ İşlem iptal edildi."
    exit 0
fi

echo ""
echo "🔄 Yedekler geri yükleniyor..."

# backup_env_files içindeki tüm dosyaları geri yükle
find backup_env_files/ -type f | while read backup_file; do
    # backup_env_files/ prefix'ini kaldır
    original_file=${backup_file#backup_env_files/}
    
    # Hedef klasörü oluştur
    target_dir=$(dirname "$original_file")
    mkdir -p "$target_dir"
    
    # Dosyayı geri yükle
    cp "$backup_file" "$original_file"
    echo "✅ Geri yüklendi: $original_file"
done

echo ""
echo "🎉 Tüm environment ve appsettings dosyaları başarıyla geri yüklendi!"
echo "📋 Geri yüklenen dosyalar:"
echo "Environment dosyaları:"
find . -name "*.env*" -not -path "./node_modules/*" -not -path "./backup_env_files/*" -type f | sort
echo ""
echo "Appsettings dosyaları:"
find B2B.Backend/ -name "appsettings*.json" -not -path "*/bin/*" | sort
