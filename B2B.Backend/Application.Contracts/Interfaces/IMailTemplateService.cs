using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

/// <summary>
/// Mail template servisi interface'i
/// </summary>
public interface IMailTemplateService
{
    /// <summary>
    /// Tüm mail template'lerini getir
    /// </summary>
    /// <returns>Mail template'leri listesi</returns>
    Task<IEnumerable<MailTemplateDto>> GetAllAsync();

    /// <summary>
    /// ID ile mail template getir
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <returns>Mail template veya null</returns>
    Task<MailTemplateDto?> GetByIdAsync(Guid id);

    /// <summary>
    /// ShortCode ile mail template getir
    /// </summary>
    /// <param name="shortCode">Template kısa kodu</param>
    /// <returns>Mail template veya null</returns>
    Task<MailTemplateDto?> GetByShortCodeAsync(string shortCode);

    /// <summary>
    /// Kategoriye göre mail template'leri getir
    /// </summary>
    /// <param name="category">Kategori</param>
    /// <returns>Mail template'leri listesi</returns>
    Task<IEnumerable<MailTemplateDto>> GetByCategoryAsync(string category);

    /// <summary>
    /// Aktif mail template'leri getir
    /// </summary>
    /// <returns>Aktif mail template'leri</returns>
    Task<IEnumerable<MailTemplateDto>> GetActiveTemplatesAsync();

    /// <summary>
    /// Mail template oluştur
    /// </summary>
    /// <param name="dto">Oluşturma DTO</param>
    /// <returns>Oluşturulan mail template</returns>
    Task<MailTemplateDto> CreateAsync(CreateMailTemplateDto dto);

    /// <summary>
    /// Mail template güncelle
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="dto">Güncelleme DTO</param>
    /// <returns>Güncellenmiş mail template</returns>
    Task<MailTemplateDto> UpdateAsync(Guid id, UpdateMailTemplateDto dto);

    /// <summary>
    /// Mail template sil
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <returns>Başarılı mı?</returns>
    Task<bool> DeleteAsync(Guid id);

    /// <summary>
    /// Mail template aktif/pasif durumunu değiştir
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <returns>Yeni durum</returns>
    Task<bool> ToggleActiveAsync(Guid id);

    /// <summary>
    /// Template içeriğini değişkenlerle render et (önizleme için)
    /// </summary>
    /// <param name="shortCode">Template kısa kodu</param>
    /// <param name="variables">Değişkenler</param>
    /// <returns>Render edilmiş içerik (subject ve content)</returns>
    Task<(string subject, string content)> RenderTemplateAsync(string shortCode, Dictionary<string, string> variables);

    /// <summary>
    /// Template içeriğini değişkenler ve döngü verileriyle render et (gelişmiş)
    /// </summary>
    /// <param name="shortCode">Template kısa kodu</param>
    /// <param name="variables">Basit değişkenler</param>
    /// <param name="loopData">Döngü verileri (döngü adı -> liste)</param>
    /// <returns>Render edilmiş içerik (subject ve content)</returns>
    Task<(string subject, string content)> RenderTemplateWithLoopsAsync(string shortCode, Dictionary<string, string> variables, Dictionary<string, List<Dictionary<string, string>>> loopData);

    /// <summary>
    /// Template'de kullanılan değişkenleri getir
    /// </summary>
    /// <param name="shortCode">Template kısa kodu</param>
    /// <returns>Değişken listesi</returns>
    Task<List<string>?> GetTemplateVariablesAsync(string shortCode);
}
