namespace Core.Events;

/// <summary>
/// MailAPI'ye mail gönderme isteği event'i
/// </summary>
public class SendMailRequested
{
    /// <summary>
    /// Mail template kısa kodu
    /// </summary>
    public string TemplateShortCode { get; set; } = null!;

    /// <summary>
    /// Alıcı e-posta adresi
    /// </summary>
    public string ToEmail { get; set; } = null!;

    /// <summary>
    /// Alıcı adı (opsiyonel)
    /// </summary>
    public string? ToName { get; set; }

    /// <summary>
    /// Template değişkenleri (key-value pairs)
    /// Örn: {"customerName": "Ahmet Yılmaz", "trackingNumber": "YK123456", "orderNumber": "ORD-001"}
    /// </summary>
    public Dictionary<string, string> Variables { get; set; } = new();

    /// <summary>
    /// Gönderici e-posta adresi (opsiyonel, template'den alınır)
    /// </summary>
    public string? FromEmail { get; set; }

    /// <summary>
    /// Gönderici adı (opsiyonel, template'den alınır)
    /// </summary>
    public string? FromName { get; set; }

    /// <summary>
    /// CC alıcıları (opsiyonel)
    /// </summary>
    public List<string>? CcEmails { get; set; }

    /// <summary>
    /// BCC alıcıları (opsiyonel)
    /// </summary>
    public List<string>? BccEmails { get; set; }

    /// <summary>
    /// Öncelik (1: Düşük, 2: Normal, 3: Yüksek)
    /// </summary>
    public int Priority { get; set; } = 2;

    /// <summary>
    /// Gönderim zamanı (opsiyonel, null ise hemen gönder)
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Event oluşturulma zamanı
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// İsteği yapan kullanıcı ID (opsiyonel)
    /// </summary>
    public Guid? RequestedByUserId { get; set; }

    /// <summary>
    /// İlgili entity ID (opsiyonel, örn: OrderId, ShipmentId)
    /// </summary>
    public Guid? RelatedEntityId { get; set; }

    /// <summary>
    /// İlgili entity tipi (opsiyonel, örn: "Order", "Shipment")
    /// </summary>
    public string? RelatedEntityType { get; set; }

    /// <summary>
    /// Özel konu (template yerine kullanılacak, opsiyonel)
    /// </summary>
    public string? CustomSubject { get; set; }

    /// <summary>
    /// Özel içerik (template yerine kullanılacak, opsiyonel)
    /// </summary>
    public string? CustomContent { get; set; }
}
