<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.2" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
        <PackageReference Include="MockQueryable.Moq" Version="8.0.0" />
        <PackageReference Include="xunit" Version="2.9.2" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
        <PackageReference Include="Moq" Version="4.20.72" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
        <PackageReference Include="MassTransit.TestFramework" Version="8.3.4" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Infrastructure\Infrastructure.csproj" />
        <ProjectReference Include="..\..\Core\Core.csproj" />
        <ProjectReference Include="..\..\Application.Contracts\Application.Contracts.csproj" />
        <ProjectReference Include="..\..\PanelApi\PanelApi.csproj" />
        <ProjectReference Include="..\..\Modules\Shipping.Abstraction\Shipping.Abstraction.csproj" />
        <ProjectReference Include="..\..\Modules\Shipping.Implementation\Shipping.Implementation.csproj" />
    </ItemGroup>

</Project>