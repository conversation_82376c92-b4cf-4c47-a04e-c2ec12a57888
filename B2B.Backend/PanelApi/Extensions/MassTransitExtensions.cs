using MassTransit;
using Core.Events;
using PanelApi.Events;

namespace PanelApi.Extensions;

public static class MassTransitExtensions
{
    public static IServiceCollection AddMassTransitConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMassTransit(x =>
        {
            // Event consumers'ları ekle
            x.AddConsumer<ShipmentCreateRequestedHandler>();
            x.AddConsumer<ShipmentCreatedHandler>();
            x.AddConsumer<CustomerShipmentNotificationHandler>();
            x.AddConsumer<AdminShipmentNotificationHandler>();
            x.AddConsumer<OrderConfirmationNotificationHandler>();

            x.UsingRabbitMq((context, cfg) =>
            {
                // MediaAPI ile aynı format kullan - appsettings.json'dan oku
                var host = configuration.GetValue<string>("RabbitMQ:Host") ?? "localhost";
                var username = configuration.GetValue<string>("RabbitMQ:Username") ?? "guest";
                var password = configuration.GetValue<string>("RabbitMQ:Password") ?? "guest";

                cfg.Host(host, h =>
                {
                    h.Username(username);
                    h.Password(password);
                });

                // MediaAPI ile uyumlu konfigürasyon - OptimizeImageEventHandler exchange kullan
                cfg.Message<OptimizeImageEvent>(e => e.SetEntityName("OptimizeImageEventHandler"));
                cfg.Publish<OptimizeImageEvent>(e => e.ExchangeType = "fanout");

                // Shipping event'leri için endpoint'ler
                cfg.ReceiveEndpoint("shipment-create-requested", e =>
                {
                    e.ConfigureConsumer<ShipmentCreateRequestedHandler>(context);
                });

                cfg.ReceiveEndpoint("shipment-created", e =>
                {
                    e.ConfigureConsumer<ShipmentCreatedHandler>(context);
                });

                cfg.ReceiveEndpoint("customer-shipment-notification", e =>
                {
                    e.ConfigureConsumer<CustomerShipmentNotificationHandler>(context);
                });

                cfg.ReceiveEndpoint("admin-shipment-notification", e =>
                {
                    e.ConfigureConsumer<AdminShipmentNotificationHandler>(context);
                });

                cfg.ReceiveEndpoint("order-confirmation-notification", e =>
                {
                    e.ConfigureConsumer<OrderConfirmationNotificationHandler>(context);
                });
            });
        });

        return services;
    }
}
