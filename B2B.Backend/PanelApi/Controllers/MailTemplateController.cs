using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;
using Core.Events;
using Modules.Mail.Abstraction.Models;

namespace PanelApi.Controllers;

/// <summary>
/// Mail template yönetimi controller'ı
/// </summary>
[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class MailTemplateController : ControllerBase
{
    private readonly IMailTemplateService _mailTemplateService;
    private readonly IPublishEndpoint _publisher;

    public MailTemplateController(IMailTemplateService mailTemplateService, IPublishEndpoint publisher)
    {
        _mailTemplateService = mailTemplateService;
        _publisher = publisher;
    }

    /// <summary>
    /// Tüm mail template'lerini listele
    /// </summary>
    [HttpGet]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult<IEnumerable<MailTemplateDto>>> GetAll()
    {
        try
        {
            var templates = await _mailTemplateService.GetAllAsync();
            return Ok(templates);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// ID ile mail template getir
    /// </summary>
    [HttpGet("{id}")]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult<MailTemplateDto>> GetById(Guid id)
    {
        try
        {
            var template = await _mailTemplateService.GetByIdAsync(id);
            if (template == null)
                return NotFound(new { message = "Mail template not found" });

            return Ok(template);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// ShortCode ile mail template getir
    /// </summary>
    [HttpGet("shortcode/{shortCode}")]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult<MailTemplateDto>> GetByShortCode(string shortCode)
    {
        try
        {
            var template = await _mailTemplateService.GetByShortCodeAsync(shortCode);
            if (template == null)
                return NotFound(new { message = "Mail template not found" });

            return Ok(template);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Kategoriye göre mail template'leri getir
    /// </summary>
    [HttpGet("category/{category}")]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult<IEnumerable<MailTemplateDto>>> GetByCategory(string category)
    {
        try
        {
            var templates = await _mailTemplateService.GetByCategoryAsync(category);
            return Ok(templates);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Aktif mail template'leri getir
    /// </summary>
    [HttpGet("active")]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult<IEnumerable<MailTemplateDto>>> GetActive()
    {
        try
        {
            var templates = await _mailTemplateService.GetActiveTemplatesAsync();
            return Ok(templates);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Mail template oluştur
    /// </summary>
    [HttpPost]
    [RequirePermission("mail", "create")]
    public async Task<ActionResult<MailTemplateDto>> Create([FromBody] CreateMailTemplateDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var template = await _mailTemplateService.CreateAsync(dto);
            return Ok(new { data = template, message = "Mail template created successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Mail template güncelle
    /// </summary>
    [HttpPut("{id}")]
    [RequirePermission("mail", "update")]
    public async Task<ActionResult<MailTemplateDto>> Update(Guid id, [FromBody] UpdateMailTemplateDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var template = await _mailTemplateService.UpdateAsync(id, dto);
            return Ok(new { data = template, message = "Mail template updated successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Mail template sil
    /// </summary>
    [HttpDelete("{id}")]
    [RequirePermission("mail", "delete")]
    public async Task<ActionResult> Delete(Guid id)
    {
        try
        {
            var result = await _mailTemplateService.DeleteAsync(id);
            if (!result)
                return NotFound(new { message = "Mail template not found" });

            return Ok(new { message = "Mail template deleted successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Mail template aktif/pasif durumunu değiştir
    /// </summary>
    [HttpPut("{id}/toggle")]
    [RequirePermission("mail", "update")]
    public async Task<ActionResult> ToggleActive(Guid id)
    {
        try
        {
            var newStatus = await _mailTemplateService.ToggleActiveAsync(id);
            return Ok(new { message = "Status updated successfully", isActive = newStatus });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Template önizleme (değişkenlerle render et)
    /// </summary>
    [HttpPost("{shortCode}/preview")]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult> Preview(string shortCode, [FromBody] Dictionary<string, string> variables)
    {
        try
        {
            var (subject, content) = await _mailTemplateService.RenderTemplateAsync(shortCode, variables);
            return Ok(new { subject, content });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Template önizleme (değişkenler ve döngülerle render et)
    /// </summary>
    [HttpPost("{shortCode}/preview-with-loops")]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult> PreviewWithLoops(string shortCode, [FromBody] TemplatePreviewWithLoopsRequest request)
    {
        try
        {
            var (subject, content) = await _mailTemplateService.RenderTemplateWithLoopsAsync(
                shortCode,
                request.Variables ?? new Dictionary<string, string>(),
                request.LoopData ?? new Dictionary<string, List<Dictionary<string, string>>>()
            );
            return Ok(new { subject, content });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Template değişkenlerini getir
    /// </summary>
    [HttpGet("{shortCode}/variables")]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult> GetVariables(string shortCode)
    {
        try
        {
            var variables = await _mailTemplateService.GetTemplateVariablesAsync(shortCode);
            return Ok(new { variables });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Test mail gönder
    /// </summary>
    [HttpPost("send-test")]
    [RequirePermission("mail", "read")]
    public async Task<ActionResult> SendTestMail([FromBody] SendTestMailDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            // Template'i bul
            var template = await _mailTemplateService.GetByShortCodeAsync(dto.TemplateShortCode);
            if (template == null)
                return NotFound(new { message = "Mail template not found" });

            if (!template.IsActive)
                return BadRequest(new { message = "Mail template is not active" });

            // Template'i render et
            var (subject, content) = await _mailTemplateService.RenderTemplateAsync(dto.TemplateShortCode, dto.Variables);

            // Test mail gönder (burada gerçek mail gönderme implementasyonu olacak)
            await _publisher.Publish(new MailMessage
            {
                Title = subject,
                MailContent = content,
                To = new List<string> { dto.RecipientEmail },
            });
            // Şimdilik sadece başarılı response döndürüyoruz
            return Ok(new { message = "Test mail sent successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}
