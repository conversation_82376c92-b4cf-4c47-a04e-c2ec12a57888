using Application.Contracts.Interfaces;
using Core.Events;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace PanelApi.Events;

/// <summary>
/// Müşteri kargo bildirim maili event handler'ı
/// MailAPI'ye SendMailRequested event'i gönderir (döngü desteği ile)
/// </summary>
public class CustomerShipmentNotificationHandler : IConsumer<CustomerShipmentNotificationRequested>
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IOrderService _orderService;
    private readonly IMailTemplateService _mailTemplateService;
    private readonly ILogger<CustomerShipmentNotificationHandler> _logger;

    public CustomerShipmentNotificationHandler(
        IPublishEndpoint publishEndpoint,
        IOrderService orderService,
        IMailTemplateService mailTemplateService,
        ILogger<CustomerShipmentNotificationHandler> logger)
    {
        _publishEndpoint = publishEndpoint;
        _orderService = orderService;
        _mailTemplateService = mailTemplateService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<CustomerShipmentNotificationRequested> context)
    {
        var notification = context.Message;

        try
        {
            _logger.LogInformation("Processing customer shipment notification for: {CustomerEmail}",
                notification.CustomerEmail);

            // Sipariş bilgilerini al
            var order = await _orderService.GetByIdAsync(notification.OrderId);
            if (order == null)
            {
                _logger.LogWarning("Order not found for shipment notification: {OrderId}", notification.OrderId);
                return;
            }

            // Basit değişkenler - template'deki tüm değişkenler
            var fullAddress = $"{order.Address?.Line1 ?? ""} {order.Address?.Line2 ?? ""}".Trim();
            if (string.IsNullOrEmpty(fullAddress))
                fullAddress = notification.DeliveryAddress;

            var variables = new Dictionary<string, string>
            {
                { "customerName", notification.CustomerName },
                { "customerPhone", order.Customer?.PhoneNumber ?? "" },
                { "customerEmail", notification.CustomerEmail },
                { "orderNumber", notification.OrderNumber },
                { "orderDate", order.CreatedAt.ToString("dd.MM.yyyy") },
                { "orderItemsTotal", (order.TotalAmount - order.TaxAmount - order.ShippingAmount).ToString("F2") },
                { "orderItemsDiscountTotal", order.DiscountAmount.ToString("F2") },
                { "orderItemsCargo", order.ShippingAmount.ToString("F2") },
                { "orderItemsKdvTotal", order.TotalAmount.ToString("F2") },
                { "deliveryAddress", fullAddress },
                { "deliveryCity", order.Address?.City ?? "" },
                { "deliveryCounty", order.Address?.District ?? "" },
                { "deliveryCountry", order.Address?.Country ?? "Türkiye" },
                { "billingAddress", fullAddress },
                { "billingCity", order.Address?.City ?? "" },
                { "billingCounty", order.Address?.District ?? "" },
                { "billingCountry", order.Address?.Country ?? "Türkiye" },
                { "sendLinkAddress", $"/orders/{order.Id}" },
                { "carrierName", notification.CarrierName },
                { "trackingNumber", notification.TrackingNumber },
                { "trackingUrl", notification.TrackingUrl ?? "" },
                { "estimatedDelivery", notification.EstimatedDeliveryDate?.ToString("dd.MM.yyyy") ?? "2-3 iş günü içinde" }
            };

            // Döngü verileri - sipariş ürünleri
            var loopData = new Dictionary<string, List<Dictionary<string, string>>>
            {
                {
                    "items", order.OrderRows.Select(row => new Dictionary<string, string>
                    {
                        { "orderRowName", row.Product?.Name ?? "Ürün" },
                        { "orderRowImage", row.Product?.Images?.FirstOrDefault()?.ThumbnailMediumPath ?? "/images/no-image.jpg" },
                        { "orderRowAmount", row.Quantity.ToString() },
                        { "orderRowPrice", row.Price.ToString("F2") },
                        { "orderRowDiscount", row.DiscountedPrice.ToString("F2") },
                        { "orderRowVariant", GetProductVariantInfo(row) }
                    }).ToList()
                }
            };

            // Template'i döngü desteği ile render et
            var (subject, content) = await _mailTemplateService.RenderTemplateWithLoopsAsync(
                "customer-shipment-notification",
                variables,
                loopData
            );

            // MailAPI'ye SendMailRequested event'i gönder
            await _publishEndpoint.Publish(new SendMailRequested
            {
                TemplateShortCode = "customer-shipment-notification",
                ToEmail = notification.CustomerEmail,
                ToName = notification.CustomerName,
                Variables = variables,
                Priority = 2, // Normal öncelik
                RelatedEntityId = notification.ShipmentId,
                RelatedEntityType = "Shipment",
                // Render edilmiş içeriği de gönder
                CustomSubject = subject,
                CustomContent = content
            });

            _logger.LogInformation("SendMailRequested event published for customer: {CustomerEmail}",
                notification.CustomerEmail);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing customer shipment notification for: {CustomerEmail}",
                notification.CustomerEmail);
            throw;
        }
    }

    private static string GetProductVariantInfo(Application.Contracts.DTOs.OrderRowDto row)
    {
        // Ürün varyant bilgilerini birleştir
        // Bu kısım ürün varyant yapınıza göre özelleştirilebilir
        if (row.Product?.AttributeMappings?.Count > 0)
        {
            var variantInfo = new List<string>();

            // Attribute mappings'den varyant bilgilerini al
            foreach (var mapping in row.Product.AttributeMappings)
            {
                if (mapping.Attribute != null && mapping.AttributeValue != null)
                {
                    variantInfo.Add($"{mapping.Attribute.Name}: {mapping.AttributeValue.Value}");
                }
            }

            return string.Join(" | ", variantInfo);
        }

        return "";
    }
}
