using MassTransit;
using Core.Events;

namespace WebApi.Extensions;

public static class MassTransitExtensions
{
    public static IServiceCollection AddMassTransitConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMassTransit(x =>
        {
            // WebApi sadece event publish ediyor, consumer yok
            // OrderConfirmationNotificationRequested event'ini publish edecek

            x.UsingRabbitMq((context, cfg) =>
            {
                // PanelApi ile aynı RabbitMQ konfigürasyonu
                var host = configuration.GetValue<string>("RabbitMQ:Host") ?? "localhost";
                var username = configuration.GetValue<string>("RabbitMQ:Username") ?? "guest";
                var password = configuration.GetValue<string>("RabbitMQ:Password") ?? "guest";

                cfg.Host(host, h =>
                {
                    h.Username(username);
                    h.Password(password);
                });

                // OrderConfirmationNotificationRequested event için exchange konfigürasyonu
                cfg.Message<OrderConfirmationNotificationRequested>(e => e.SetEntity<PERSON>ame("order-confirmation-notification"));
                cfg.Publish<OrderConfirmationNotificationRequested>(e => e.ExchangeType = "fanout");
            });
        });

        return services;
    }
}
