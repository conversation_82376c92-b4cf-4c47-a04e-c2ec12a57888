using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class CartService : ICartService
{
    private readonly ICartRepository _cartRepository;
    private readonly ICartItemRepository _cartItemRepository;
    private readonly IEncryptionService _encryptionService;

    public CartService(
        ICartRepository cartRepository,
        ICartItemRepository cartItemRepository,
        IEncryptionService encryptionService)
    {
        _cartRepository = cartRepository;
        _cartItemRepository = cartItemRepository;
        _encryptionService = encryptionService;
    }

    public async Task<List<CartListDto>> GetListAsync(int? page = null, int? pageSize = null)
    {
        var carts = await _cartRepository.GetPagedAsync(page, pageSize);
        var result = new List<CartListDto>();

        foreach (var cart in carts.Where(c => !c.Is<PERSON>))
        {
            var itemCount = await _cartRepository.GetCartItemCountAsync(cart.Id);
            var totalAmount = await _cartRepository.GetCartTotalAsync(cart.Id);

            result.Add(new CartListDto
            {
                Id = cart.Id,
                CustomerId = cart.CustomerId,
                CustomerName = _encryptionService.DecryptIfNotEmpty(cart.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
                CustomerEmail = _encryptionService.DecryptIfNotEmpty(cart.Customer?.Email),
                ItemCount = itemCount,
                TotalAmount = totalAmount,
                LastUpdated = cart.UpdatedAt,
                CreatedAt = cart.CreatedAt
            });
        }

        return result.OrderByDescending(c => c.LastUpdated).ToList();
    }

    public async Task<CartDto?> GetByIdAsync(Guid id)
    {
        var cart = await _cartRepository.GetWithItemsAndProductsAsync(id);
        if (cart == null || cart.IsDeleted)
            return null;

        return await MapToCartDto(cart);
    }

    public async Task<CartDto?> GetByCustomerIdAsync(Guid customerId)
    {
        var cart = await _cartRepository.GetCustomerCartWithItemsAsync(customerId);
        if (cart == null || cart.IsDeleted)
            return null;

        return await MapToCartDto(cart);
    }

    public async Task<CartSummaryDto?> GetCartSummaryAsync(Guid cartId)
    {
        var cart = await _cartRepository.GetByIdAsync(cartId);
        if (cart == null || cart.IsDeleted)
            return null;

        var itemCount = await _cartRepository.GetCartItemCountAsync(cartId);
        var totalAmount = await _cartRepository.GetCartTotalAsync(cartId);
        var uniqueProducts = await _cartItemRepository.Query()
            .Where(ci => ci.CartId == cartId && !ci.IsDeleted)
            .Select(ci => ci.ProductId)
            .Distinct()
            .CountAsync();

        return new CartSummaryDto
        {
            CartId = cartId,
            CustomerId = cart.CustomerId,
            TotalItems = itemCount,
            TotalAmount = totalAmount,
            UniqueProducts = uniqueProducts,
            LastActivity = cart.UpdatedAt
        };
    }

    public async Task<Guid> AddItemAsync(CartItemCreateDto dto)
    {
        // Check if product already exists in cart
        var existingItem = await _cartItemRepository.GetByCartAndProductAsync(dto.CartId, dto.ProductId);
        
        if (existingItem != null)
        {
            // Update existing item quantity
            existingItem.Quantity += dto.Quantity;
            existingItem.UnitPrice = dto.UnitPrice; // Update price in case it changed
            existingItem.UpdatedAt = DateTime.UtcNow;

            _cartItemRepository.Update(existingItem);
            await _cartItemRepository.SaveChangesAsync();

            // Add to history
            await _cartItemRepository.AddToHistoryAsync(existingItem, ChangeType.Updated, Guid.Empty);
            await _cartItemRepository.SaveChangesAsync();

            return existingItem.Id;
        }

        // Create new cart item
        var cartItem = new CartItem
        {
            Id = Guid.CreateVersion7(),
            CartId = dto.CartId,
            ProductId = dto.ProductId,
            Quantity = dto.Quantity,
            UnitPrice = dto.UnitPrice,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _cartItemRepository.AddAsync(cartItem);
        await _cartItemRepository.SaveChangesAsync();

        // Add to history
        await _cartItemRepository.AddToHistoryAsync(cartItem, ChangeType.Created, Guid.Empty);
        await _cartItemRepository.SaveChangesAsync();

        return cartItem.Id;
    }

    public async Task UpdateItemAsync(CartItemUpdateDto dto)
    {
        var cartItem = await _cartItemRepository.GetByIdAsync(dto.Id);
        if (cartItem == null || cartItem.IsDeleted)
            throw new ArgumentException("Cart item not found");

        cartItem.Quantity = dto.Quantity;
        cartItem.UnitPrice = dto.UnitPrice;
        cartItem.UpdatedAt = DateTime.UtcNow;

        _cartItemRepository.Update(cartItem);
        await _cartItemRepository.SaveChangesAsync();

        // Add to history
        await _cartItemRepository.AddToHistoryAsync(cartItem, ChangeType.Updated, Guid.Empty);
        await _cartItemRepository.SaveChangesAsync();
    }

    public async Task RemoveItemAsync(Guid cartItemId)
    {
        var cartItem = await _cartItemRepository.GetByIdAsync(cartItemId);
        if (cartItem == null || cartItem.IsDeleted)
            throw new ArgumentException("Cart item not found");

        cartItem.IsDeleted = true;
        cartItem.UpdatedAt = DateTime.UtcNow;

        _cartItemRepository.Update(cartItem);
        await _cartItemRepository.SaveChangesAsync();

        // Add to history
        await _cartItemRepository.AddToHistoryAsync(cartItem, ChangeType.Deleted, Guid.Empty);
        await _cartItemRepository.SaveChangesAsync();
    }

    public async Task<bool> ClearCartAsync(Guid cartId)
    {
        var cartItems = await _cartItemRepository.GetByCartIdAsync(cartId);
        
        foreach (var item in cartItems)
        {
            item.IsDeleted = true;
            item.UpdatedAt = DateTime.UtcNow;
            await _cartItemRepository.AddToHistoryAsync(item, ChangeType.Deleted, Guid.Empty);
        }

        _cartItemRepository.UpdateRange(cartItems);
        await _cartItemRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> BulkRemoveItemsAsync(BulkCartItemDeleteDto dto)
    {
        var cartItems = await _cartItemRepository.Query()
            .Where(ci => dto.CartItemIds.Contains(ci.Id) && !ci.IsDeleted)
            .ToListAsync();

        foreach (var item in cartItems)
        {
            item.IsDeleted = true;
            item.UpdatedAt = DateTime.UtcNow;
            await _cartItemRepository.AddToHistoryAsync(item, ChangeType.Deleted, Guid.Empty);
        }

        _cartItemRepository.UpdateRange(cartItems);
        await _cartItemRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> IsCartEmptyAsync(Guid cartId)
    {
        return await _cartRepository.IsCartEmptyAsync(cartId);
    }

    public async Task<int> GetCartItemCountAsync(Guid cartId)
    {
        return await _cartRepository.GetCartItemCountAsync(cartId);
    }

    public async Task<decimal> GetCartTotalAsync(Guid cartId)
    {
        return await _cartRepository.GetCartTotalAsync(cartId);
    }

    public async Task<bool> IsProductInCartAsync(Guid cartId, Guid productId)
    {
        return await _cartRepository.IsProductInCartAsync(cartId, productId);
    }

    public async Task<CartDto?> GetCustomerCartAsync(Guid customerId)
    {
        return await GetByCustomerIdAsync(customerId);
    }

    public async Task<bool> ClearCustomerCartAsync(Guid customerId)
    {
        var cart = await _cartRepository.GetByCustomerIdAsync(customerId);
        if (cart == null)
            return false;

        return await ClearCartAsync(cart.Id);
    }

    public async Task<int> GetCustomerCartItemCountAsync(Guid customerId)
    {
        var cart = await _cartRepository.GetByCustomerIdAsync(customerId);
        if (cart == null)
            return 0;

        return await GetCartItemCountAsync(cart.Id);
    }

    public async Task<decimal> GetCustomerCartTotalAsync(Guid customerId)
    {
        var cart = await _cartRepository.GetByCustomerIdAsync(customerId);
        if (cart == null)
            return 0;

        return await GetCartTotalAsync(cart.Id);
    }

    private async Task<CartDto> MapToCartDto(Cart cart)
    {
        var items = new List<CartItemDto>();
        
        foreach (var item in cart.Items.Where(i => !i.IsDeleted))
        {
            items.Add(new CartItemDto
            {
                Id = item.Id,
                CartId = item.CartId,
                ProductId = item.ProductId,
                ProductName = item.Product?.Name ?? "Bilinmeyen Ürün",
                ProductSku = item.Product?.Sku ?? "",
                ProductImage = item.Product?.Images?.FirstOrDefault(x=>x.IsMainImage)?.ThumbnailMdPath,
                Quantity = item.Quantity,
                UnitPrice = item.Product?.Price ?? item.UnitPrice,
                DiscountedPrice = item.Product?.DiscountedPrice,
                TotalPrice = item.Quantity * item.Product?.DiscountedPrice ?? item.Quantity * item.UnitPrice,
                StockQuantity = item.Product?.StockQuantity ?? 0,
                IsInStock = item.Product?.IsActive == true && item.Product?.StockQuantity >= item.Quantity,
                CreatedAt = item.CreatedAt,
                UpdatedAt = item.UpdatedAt
            });
        }

        return new CartDto
        {
            Id = cart.Id,
            CustomerId = cart.CustomerId,
            CustomerName = _encryptionService.DecryptIfNotEmpty(cart.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
            Items = items,
            TotalItems = items.Sum(i => (int)i.Quantity),
            TotalAmount = items.Sum(i => i.TotalPrice),
            CreatedAt = cart.CreatedAt,
            UpdatedAt = cart.UpdatedAt
        };
    }

    public async Task<CartAnalyticsDto> GetAnalyticsAsync()
    {
        var totalActiveCarts = await _cartRepository.GetTotalActiveCartsAsync();
        var totalAbandonedCarts = await _cartRepository.GetAbandonedCartCountAsync(7);
        var averageCartValue = await _cartRepository.GetAverageCartValueAsync();
        var totalCartValue = await _cartRepository.GetTotalCartValueAsync();
        var totalCartItems = await _cartItemRepository.Query()
            .Where(ci => !ci.IsDeleted && !ci.Cart.IsDeleted)
            .SumAsync(ci => (int)ci.Quantity);

        var topProducts = await GetTopProductsInCartsAsync(10);
        var cartsByDate = await GetCartsByDateAsync(30);

        return new CartAnalyticsDto
        {
            TotalActiveCarts = totalActiveCarts,
            TotalAbandonedCarts = totalAbandonedCarts,
            AverageCartValue = averageCartValue,
            TotalCartValue = totalCartValue,
            TotalCartItems = totalCartItems,
            TopProducts = topProducts,
            CartsByDate = cartsByDate
        };
    }

    public async Task<List<CartTopProductsDto>> GetTopProductsInCartsAsync(int count = 10)
    {
        var topProducts = await _cartItemRepository.Query()
            .Include(ci => ci.Product)
            .Where(ci => !ci.IsDeleted && !ci.Cart.IsDeleted)
            .GroupBy(ci => new { ci.ProductId, ci.Product.Name, ci.Product.Sku })
            .Select(g => new CartTopProductsDto
            {
                ProductId = g.Key.ProductId,
                ProductName = g.Key.Name,
                ProductSku = g.Key.Sku,
                TimesAddedToCart = g.Count(),
                TotalQuantity = g.Sum(ci => ci.Quantity),
                TotalValue = g.Sum(ci => ci.Quantity * ci.UnitPrice)
            })
            .OrderByDescending(p => p.TotalQuantity)
            .Take(count)
            .ToListAsync();

        return topProducts;
    }

    public async Task<List<CartByDateDto>> GetCartsByDateAsync(int days = 30)
    {
        var startDate = DateTime.UtcNow.AddDays(-days).Date;

        var cartsByDate = await _cartRepository.Query()
            .Where(c => !c.IsDeleted && c.CreatedAt >= startDate)
            .GroupBy(c => c.CreatedAt.Date)
            .Select(g => new CartByDateDto
            {
                Date = g.Key,
                CartCount = g.Count(),
                TotalValue = g.SelectMany(c => c.Items.Where(i => !i.IsDeleted))
                             .Sum(i => i.Quantity * i.UnitPrice)
            })
            .OrderBy(c => c.Date)
            .ToListAsync();

        return cartsByDate;
    }

    public async Task<List<CartListDto>> GetAbandonedCartsAsync(int daysOld = 7)
    {
        var carts = await _cartRepository.GetAbandonedCartsAsync(daysOld);
        var result = new List<CartListDto>();

        foreach (var cart in carts)
        {
            var itemCount = cart.Items.Count(i => !i.IsDeleted);
            var totalAmount = cart.Items.Where(i => !i.IsDeleted).Sum(i => i.Quantity * i.UnitPrice);

            result.Add(new CartListDto
            {
                Id = cart.Id,
                CustomerId = cart.CustomerId,
                CustomerName = _encryptionService.DecryptIfNotEmpty(cart.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
                CustomerEmail = _encryptionService.DecryptIfNotEmpty(cart.Customer?.Email),
                ItemCount = itemCount,
                TotalAmount = totalAmount,
                LastUpdated = cart.UpdatedAt,
                CreatedAt = cart.CreatedAt
            });
        }

        return result;
    }

    public async Task<int> GetAbandonedCartCountAsync(int daysOld = 7)
    {
        return await _cartRepository.GetAbandonedCartCountAsync(daysOld);
    }

    public async Task<bool> CleanupAbandonedCartsAsync(int daysOld = 30)
    {
        var abandonedCarts = await _cartRepository.GetAbandonedCartsAsync(daysOld);

        foreach (var cart in abandonedCarts)
        {
            await ClearCartAsync(cart.Id);
        }

        return true;
    }

    public async Task<List<CartListDto>> SearchCartsAsync(string searchTerm)
    {
        var carts = await _cartRepository.SearchCartsAsync(searchTerm);
        var result = new List<CartListDto>();

        foreach (var cart in carts)
        {
            var itemCount = cart.Items.Count(i => !i.IsDeleted);
            var totalAmount = cart.Items.Where(i => !i.IsDeleted).Sum(i => i.Quantity * i.UnitPrice);

            result.Add(new CartListDto
            {
                Id = cart.Id,
                CustomerId = cart.CustomerId,
                CustomerName = _encryptionService.DecryptIfNotEmpty(cart.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
                CustomerEmail = _encryptionService.DecryptIfNotEmpty(cart.Customer?.Email),
                ItemCount = itemCount,
                TotalAmount = totalAmount,
                LastUpdated = cart.UpdatedAt,
                CreatedAt = cart.CreatedAt
            });
        }

        return result;
    }

    public async Task<List<CartListDto>> GetCartsByCustomerAsync(Guid customerId)
    {
        var carts = await _cartRepository.GetCartsByCustomerAsync(customerId);
        var result = new List<CartListDto>();

        foreach (var cart in carts)
        {
            var itemCount = cart.Items.Count(i => !i.IsDeleted);
            var totalAmount = cart.Items.Where(i => !i.IsDeleted).Sum(i => i.Quantity * i.UnitPrice);

            result.Add(new CartListDto
            {
                Id = cart.Id,
                CustomerId = cart.CustomerId,
                CustomerName = _encryptionService.DecryptIfNotEmpty(cart.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
                CustomerEmail = _encryptionService.DecryptIfNotEmpty(cart.Customer?.Email),
                ItemCount = itemCount,
                TotalAmount = totalAmount,
                LastUpdated = cart.UpdatedAt,
                CreatedAt = cart.CreatedAt
            });
        }

        return result;
    }

    public async Task<List<CartListDto>> GetActiveCartsAsync()
    {
        var carts = await _cartRepository.GetActiveCartsAsync();
        var result = new List<CartListDto>();

        foreach (var cart in carts)
        {
            var itemCount = cart.Items.Count(i => !i.IsDeleted);
            var totalAmount = cart.Items.Where(i => !i.IsDeleted).Sum(i => i.Quantity * i.UnitPrice);

            result.Add(new CartListDto
            {
                Id = cart.Id,
                CustomerId = cart.CustomerId,
                CustomerName = _encryptionService.DecryptIfNotEmpty(cart.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
                CustomerEmail = _encryptionService.DecryptIfNotEmpty(cart.Customer?.Email),
                ItemCount = itemCount,
                TotalAmount = totalAmount,
                LastUpdated = cart.UpdatedAt,
                CreatedAt = cart.CreatedAt
            });
        }

        return result;
    }

    public async Task<List<CartListDto>> GetEmptyCartsAsync()
    {
        var carts = await _cartRepository.GetEmptyCartsAsync();
        var result = new List<CartListDto>();

        foreach (var cart in carts)
        {
            result.Add(new CartListDto
            {
                Id = cart.Id,
                CustomerId = cart.CustomerId,
                CustomerName = _encryptionService.DecryptIfNotEmpty(cart.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
                CustomerEmail = _encryptionService.DecryptIfNotEmpty(cart.Customer?.Email),
                ItemCount = 0,
                TotalAmount = 0,
                LastUpdated = cart.UpdatedAt,
                CreatedAt = cart.CreatedAt
            });
        }

        return result;
    }

    public async Task<bool> ValidateCartItemAsync(Guid cartItemId)
    {
        return await _cartItemRepository.ValidateCartItemAsync(cartItemId);
    }

    public async Task<List<CartItemDto>> GetOutOfStockItemsAsync(Guid cartId)
    {
        var outOfStockItems = await _cartItemRepository.GetOutOfStockItemsAsync(cartId);
        var result = new List<CartItemDto>();

        foreach (var item in outOfStockItems)
        {
            result.Add(new CartItemDto
            {
                Id = item.Id,
                CartId = item.CartId,
                ProductId = item.ProductId,
                ProductName = item.Product?.Name ?? "Bilinmeyen Ürün",
                ProductSku = item.Product?.Sku ?? "",
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                TotalPrice = item.Quantity * item.UnitPrice,
                StockQuantity = item.Product?.StockQuantity ?? 0,
                IsInStock = false,
                CreatedAt = item.CreatedAt,
                UpdatedAt = item.UpdatedAt
            });
        }

        return result;
    }

    public async Task<bool> UpdateCartItemPricesAsync(Guid cartId)
    {
        var cartItems = await _cartItemRepository.GetWithProductsAsync(cartId);
        bool hasUpdates = false;

        foreach (var item in cartItems)
        {
            if (item.Product != null && item.Product.Price.HasValue && item.UnitPrice != item.Product.Price.Value)
            {
                item.UnitPrice = item.Product.Price.Value;
                item.UpdatedAt = DateTime.UtcNow;
                await _cartItemRepository.AddToHistoryAsync(item, ChangeType.Updated, Guid.Empty);
                hasUpdates = true;
            }
        }

        if (hasUpdates)
        {
            _cartItemRepository.UpdateRange(cartItems);
            await _cartItemRepository.SaveChangesAsync();
        }

        return hasUpdates;
    }
}
