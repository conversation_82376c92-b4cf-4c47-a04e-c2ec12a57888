# Database Configuration
POSTGRES_USER=dbuser
POSTGRES_PASSWORD=dbpassword
POSTGRES_DB=dbname

# RabbitMQ Configuration
RABBITMQ_USER=rabbitmq
RABBITMQ_PASS=rabbitmq

# PgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=password

# .NET API Configuration
ASPNETCORE_ENVIRONMENT=Development

# Database Connection (mevcut kodlarda "VeriTabani" kullanılıyor)
ConnectionStrings__VeriTabani=Host=postgres;Database=dbname;Username=dbuser;Password=dbpassword

# NextAuth Configuration (mevcut kodlarda "NextAuth:Secret" kullanılıyor)
NextAuth__Secret=very-secret-jwt-key

NEXTAUTH_SECRET=very-secret-jwt-key
# RabbitMQ Configuration (mevcut kodlarda bu format kullanılıyor)
RabbitMQ__Host=rabbitmq
RabbitMQ__Username=rabbitmq
RabbitMQ__Password=rabbitmq
RabbitMQ__Port=5672
RabbitMQ__VirtualHost=/
RabbitMQ__MailQueue=mail-queue

# API URLs (MediaAPI'de kullanılan format)
ApiSettings__UygulamaApiUrl=http://panel-api:5000

# External URLs (for frontend)
NEXT_PUBLIC_API_GATEWAY_URL=https://futureapi.ukscreative.com

# SMTP Configuration (MailAPI'de "SmtpSettings" section kullanılıyor)
SmtpSettings__Server=smtp.gmail.com
SmtpSettings__Port=587
SmtpSettings__Username=<EMAIL>
SmtpSettings__Password=your-app-password
SmtpSettings__SenderEmail=<EMAIL>
SmtpSettings__SenderName=B2B Platform
SmtpSettings__EnableSsl=true

# File Storage Configuration (MediaAPI'de "StorageSettings" kullanılıyor)
StorageSettings__Strategy=Local
StorageSettings__Path=/app/wwwroot/images

# Logging Configuration
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft=Warning
Logging__LogLevel__Microsoft.EntityFrameworkCore=Warning

# CORS Configuration
AllowedHosts=*

# CORS Hosts (ApiGateway için)
CorsHosts__0=http://localhost:3000
CorsHosts__1=http://localhost:3001
CorsHosts__2=http://b2b-frontend:3000
CorsHosts__3=http://vineta-theme:3000
CorsHosts__4=https://future.ukscreative.com
CorsHosts__5=https://futurepanel.ukscreative.com
CorsHosts__6=https://futureapi.ukscreative.com
