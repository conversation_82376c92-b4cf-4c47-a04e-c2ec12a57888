{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.EntityFrameworkCore.Infrastructure": "Warning", "Microsoft.EntityFrameworkCore.Query": "Warning", "MassTransit": "Information"}}, "StorageSettings": {"TempPath": "wwwroot/temp"}, "RabbitMQ": {"Host": "localhost", "Username": "ukscreative", "Password": "ukscreative"}}