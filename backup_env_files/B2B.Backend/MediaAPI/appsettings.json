{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "MassTransit": "Information"}}, "AllowedHosts": "*", "StorageSettings": {"Strategy": "Local", "LocalPath": "wwwroot/images", "BaseUrl": "http://localhost:33802/images", "CdnBaseUrl": "https://cdn.example.com", "CdnApiKey": "api-key-placeholder"}, "ImageSettings": {"ThumbnailWidth": 300, "ThumbnailHeight": 300, "WebpQuality": 80}, "RabbitMQ": {"Host": "localhost", "Username": "ukscreative", "Password": "ukscreative"}, "ApiSettings": {"UygulamaApiUrl": "http://localhost:33800"}, "CorsSettings": {"AllowedOrigins": ["http://localhost:3000"]}}