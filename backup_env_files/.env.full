# ========================================
# B2B PROJECT - COMPLETE ENVIRONMENT CONFIGURATION
# ========================================
# Bu dosya tüm projelerdeki ortam değişkenlerini içerir
# Canlıya alım sırasında bu dosyayı .env olarak kopyalayın

# ========================================
# DATABASE CONFIGURATION
# ========================================
POSTGRES_USER=ukscreative
POSTGRES_PASSWORD=UksCreative@2025!
POSTGRES_DB=b2b

# Database Connection String (Backend API'ler için)
ConnectionStrings__VeriTabani=Host=postgres;Port=5432;Database=b2b;Username=ukscreative;Password=UksCreative@2025!

# ========================================
# RABBITMQ CONFIGURATION
# ========================================
RABBITMQ_USER=ukscreative
RABBITMQ_PASS=ukscreative

# RabbitMQ Settings (Backend API'ler için)
RabbitMQ__Host=rabbitmq
RabbitMQ__Username=ukscreative
RabbitMQ__Password=ukscreative
RabbitMQ__Port=5672
RabbitMQ__VirtualHost=/
RabbitMQ__MailQueue=mail-queue

# ========================================
# PGADMIN CONFIGURATION
# ========================================
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=UksCreative@2025!

# ========================================
# .NET CORE CONFIGURATION
# ========================================
ASPNETCORE_ENVIRONMENT=Production
AllowedHosts=*

# ========================================
# JWT & NEXTAUTH CONFIGURATION
# ========================================
# Panel için JWT Secret
JwtSecret=acc6ee8e70bd7bdef6d11e8b800991dc7434b3571d248e3cb7ec5f543c955aed4ee0244b4355a430949c0d7070bdaa9cf6e65410f926eed9b7080f6972c40d63e798187cdd8d393b1e0979edd2392286da4be1a553c62e891819dbac399f1bcf86d6248b422841061664de32c7b351203fa059281eee2ef9cc3a31f7ee4ad262a5f970acfd2937ce70fe3194286947ade017e1ada9f7b70b919fb9de7d4f27673abbf0e99f19ae190dcc15a15688b5e37b572b366caea7f2ee83076572760bed4e85da5005a184082a08ad5f38e637ba1813e8245e402f7325c7dbdc150c9f592b3a59c72c33d57e42fa40fbd7a17289d8aeb4c39af92875101dfc07d3105767

# NextAuth Configuration (Panel için)
NextAuth__Secret=acc6ee8e70bd7bdef6d11e8b800991dc7434b3571d248e3cb7ec5f543c955aed4ee0244b4355a430949c0d7070bdaa9cf6e65410f926eed9b7080f6972c40d63e798187cdd8d393b1e0979edd2392286da4be1a553c62e891819dbac399f1bcf86d6248b422841061664de32c7b351203fa059281eee2ef9cc3a31f7ee4ad262a5f970acfd2937ce70fe3194286947ade017e1ada9f7b70b919fb9de7d4f27673abbf0e99f19ae190dcc15a15688b5e37b572b366caea7f2ee83076572760bed4e85da5005a184082a08ad5f38e637ba1813e8245e402f7325c7dbdc150c9f592b3a59c72c33d57e42fa40fbd7a17289d8aeb4c39af92875101dfc07d3105767
NextAuth__SecretPanel=acc6ee8e70bd7bdef6d11e8b800991dc7434b3571d248e3cb7ec5f543c955aed4ee0244b4355a430949c0d7070bdaa9cf6e65410f926eed9b7080f6972c40d63e798187cdd8d393b1e0979edd2392286da4be1a553c62e891819dbac399f1bcf86d6248b422841061664de32c7b351203fa059281eee2ef9cc3a31f7ee4ad262a5f970acfd2937ce70fe3194286947ade017e1ada9f7b70b919fb9de7d4f27673abbf0e99f19ae190dcc15a15688b5e37b572b366caea7f2ee83076572760bed4e85da5005a184082a08ad5f38e637ba1813e8245e402f7325c7dbdc150c9f592b3a59c72c33d57e42fa40fbd7a17289d8aeb4c39af92875101dfc07d3105767

# NextAuth Configuration (Web/B2C için)
NextAuth__SecretWeb=2606b189ff4aa35cb4e520c5cd4519b5a920c13455909bea04679a61d64c4b68555aa2b53a1a5e7f953cf683592aae12cf000e6aeb4242daa850da6f9ef5891c2f9abe9194282ec6dbb46eeb6099a02ac309e0939675fd77d9994c25900fee2bacf853d4df6daae6c13e10a7a812431fd52d64d4c4c06c0577e85849ca63892c4186b78c6b08d6a642ad5a90c9bb4e4c8c10892ea13dc17965584ddbc487c9669d7ce04b9be705fdbb21f942e87d91a11edc87e2ddc222af8e33543fa9193088e733cdcb8495405940882f30a6a7c653353db300ee755f558fb9a6255bebf8225e3699986f04dcae4daab432727cd4c0bb0013d2fb300d23cb199a39af39a8ef

# Frontend NextAuth Configuration
NEXTAUTH_SECRET=acc6ee8e70bd7bdef6d11e8b800991dc7434b3571d248e3cb7ec5f543c955aed4ee0244b4355a430949c0d7070bdaa9cf6e65410f926eed9b7080f6972c40d63e798187cdd8d393b1e0979edd2392286da4be1a553c62e891819dbac399f1bcf86d6248b422841061664de32c7b351203fa059281eee2ef9cc3a31f7ee4ad262a5f970acfd2937ce70fe3194286947ade017e1ada9f7b70b919fb9de7d4f27673abbf0e99f19ae190dcc15a15688b5e37b572b366caea7f2ee83076572760bed4e85da5005a184082a08ad5f38e637ba1813e8245e402f7325c7dbdc150c9f592b3a59c72c33d57e42fa40fbd7a17289d8aeb4c39af92875101dfc07d3105767

# ========================================
# API URLS & GATEWAY CONFIGURATION
# ========================================
# API Gateway URL (Frontend'ler için)
NEXT_PUBLIC_API_GATEWAY_URL=https://futureapi.ukscreative.com

# Internal API URLs
ApiSettings__UygulamaApiUrl=http://panel-api:5000

# ========================================
# FRONTEND CONFIGURATION
# ========================================
# B2B Frontend (Panel)
NEXTAUTH_URL=https://futurepanel.ukscreative.com
NEXT_PUBLIC_API_URL=https://futureapi.ukscreative.com/admin-api

# Company Information
NEXT_PUBLIC_COMPANY_NAME=Future Cosmetics
# ========================================
# CORS CONFIGURATION
# ========================================
CorsHosts__0=http://localhost:3000
CorsHosts__1=http://localhost:3001
CorsHosts__2=http://b2b-frontend:3000
CorsHosts__3=http://vineta-theme:3000
CorsHosts__4=https://future.ukscreative.com
CorsHosts__5=https://futurepanel.ukscreative.com
CorsHosts__6=https://futureapi.ukscreative.com

# ========================================
# SMTP & MAIL CONFIGURATION
# ========================================
SmtpSettings__Server=smtpout.secureserver.net
SmtpSettings__Port=587
SmtpSettings__Username=<EMAIL>
SmtpSettings__Password=cQy.bUJDLJBC5VR
SmtpSettings__SenderEmail=<EMAIL>
SmtpSettings__SenderName=B2B Platform
SmtpSettings__EnableSsl=false

# ========================================
# FILE STORAGE & MEDIA CONFIGURATION
# ========================================
StorageSettings__Strategy=Local
StorageSettings__Path=/app/wwwroot/images
StorageSettings__BaseUrl=https://futurecdn.ukscreative.com/images
StorageSettings__LocalPath=wwwroot/images
StorageSettings__CdnBaseUrl=https://cdn.example.com
StorageSettings__CdnApiKey=api-key-placeholder
StorageSettings__TempPath=wwwroot/temp

# Image Processing Settings
ImageSettings__ThumbnailWidth=300
ImageSettings__ThumbnailHeight=300
ImageSettings__WebpQuality=80

# ========================================
# SHIPPING CONFIGURATION
# ========================================
# Yurtiçi Kargo (Production)
Shipping__Yurtici__TestMode=false
Shipping__Yurtici__ApiUrl=https://api.yurtici.com.tr
Shipping__Yurtici__Timeout=30
Shipping__Yurtici__MaxRetries=3
Shipping__Yurtici__wsUserName=3059G1147107476
Shipping__Yurtici__wsPassword=A1f6FD263G9gdj8r
Shipping__Yurtici__wsLanguage=TR

# Yurtiçi Kargo (Test)
Shipping__YurticiTest__TestMode=true
Shipping__YurticiTest__ApiUrl=https://api.yurtici.com.tr
Shipping__YurticiTest__Timeout=30
Shipping__YurticiTest__MaxRetries=3
Shipping__YurticiTest__wsUserName=YKTEST
Shipping__YurticiTest__wsPassword=YK
Shipping__YurticiTest__wsLanguage=TR

# ========================================
# LOGGING CONFIGURATION
# ========================================
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft=Warning
Logging__LogLevel__Microsoft.AspNetCore=Warning
Logging__LogLevel__Microsoft.EntityFrameworkCore=Warning
Logging__LogLevel__Microsoft.EntityFrameworkCore.Database.Command=Warning
Logging__LogLevel__Microsoft.EntityFrameworkCore.Infrastructure=Warning
Logging__LogLevel__Microsoft.EntityFrameworkCore.Query=Warning
Logging__LogLevel__MassTransit=Information

# ========================================
# THEME CONFIGURATION (Vineta)
# ========================================
# Vineta Theme Defaults
DEFAULT_LANGUAGE=tr
DEFAULT_TIMEZONE=Europe/Istanbul
DEFAULT_CURRENCY=TRY
DEFAULT_COUNTRY=TR
DEFAULT_LOCALE=tr_TR

# ========================================
# DOCKER & PRODUCTION SPECIFIC
# ========================================
# Node.js Environment
NODE_ENV=production
HOSTNAME=0.0.0.0
CI=false

# ========================================
# DEVELOPMENT OVERRIDES
# ========================================
# Bu değişkenler development ortamında override edilebilir
# Development için ayrı .env.development dosyası oluşturulabilir

# Development Database (localhost için)
# ConnectionStrings__VeriTabani=Host=localhost;Port=5432;Database=b2b;Username=ukscreative;Password=UksCreative@2025!

# Development API URLs
# NEXT_PUBLIC_API_GATEWAY_URL=http://localhost:33800
# NEXT_PUBLIC_API_URL=http://localhost:33800/admin-api
# NEXTAUTH_URL=http://localhost:3000

# Development RabbitMQ
# RabbitMQ__Host=localhost

# Development Storage
# StorageSettings__BaseUrl=http://localhost:33802/images

# ========================================
# ADDITIONAL CONFIGURATIONS
# ========================================

# NextAuth URL Configuration (Backend için)
NextAuth__Url=https://futurepanel.ukscreative.com

# CORS Settings for MediaAPI
CorsSettings__AllowedOrigins__0=http://localhost:3000
CorsSettings__AllowedOrigins__1=http://localhost:3001
CorsSettings__AllowedOrigins__2=http://b2b-frontend:3000
CorsSettings__AllowedOrigins__3=http://vineta-theme:3000
CorsSettings__AllowedOrigins__4=https://future.ukscreative.com
CorsSettings__AllowedOrigins__5=https://futurepanel.ukscreative.com

# API Base URL for Themes
API_BASE_URL=https://futureapi.ukscreative.com

# ========================================
# EXAMPLE VALUES FOR REFERENCE
# ========================================
# Bu değerler örnek olarak verilmiştir, gerçek değerlerle değiştirilmelidir

# Example API Keys (Placeholder)
# API_USERNAME=your_api_username
# API_PASSWORD=your_api_password
# API_TOKEN=your_api_token

# Example SMTP for Gmail
# SmtpSettings__Server=smtp.gmail.com
# SmtpSettings__Port=587
# SmtpSettings__Username=<EMAIL>
# SmtpSettings__Password=your-app-password
# SmtpSettings__EnableSsl=true

# ========================================
# NOTES & INSTRUCTIONS
# ========================================
# 1. Bu dosyayı canlıya alırken .env olarak kopyalayın
# 2. Güvenlik için production'da farklı şifreler kullanın
# 3. SMTP ayarlarını gerçek mail sunucunuza göre güncelleyin
# 4. Domain adreslerini gerçek domain'lerinizle değiştirin
# 5. API anahtarlarını gerçek değerlerle değiştirin
# 6. Development için ayrı .env.development dosyası oluşturun
